const { db } = require('../db/database');
const { v4: uuidv4 } = require('uuid');

class Transaction {
  /**
   * Create a new transaction
   * @param {Object} transactionData - Transaction data
   * @returns {Promise<Object>} Created transaction
   */
  static async create(transactionData) {
    return new Promise((resolve, reject) => {
      const {
        user_id,
        plan_id,
        order_id,
        amount_idr,
        payment_method = 'midtrans',
        status = 'pending',
        midtrans_token = null,
        midtrans_redirect_url = null
      } = transactionData;

      const id = uuidv4();
      const created_at = new Date().toISOString();

      const sql = `
        INSERT INTO transactions (
          id, user_id, plan_id, order_id, amount_idr,
          payment_method, status, midtrans_token, midtrans_redirect_url, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      db.run(sql, [
        id, user_id, plan_id, order_id, amount_idr,
        payment_method, status, midtrans_token, midtrans_redirect_url, created_at
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({
            id,
            user_id,
            plan_id,
            order_id,
            amount_idr,
            payment_method,
            status,
            midtrans_token,
            midtrans_redirect_url,
            created_at
          });
        }
      });
    });
  }

  /**
   * Find transaction by order ID
   * @param {string} orderId - Order ID
   * @returns {Promise<Object|null>} Transaction data
   */
  static async findByOrderId(orderId) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT t.*, sp.name as plan_name, u.username, u.email
        FROM transactions t
        LEFT JOIN subscription_plans sp ON t.plan_id = sp.id
        LEFT JOIN users u ON t.user_id = u.id
        WHERE t.order_id = ?
      `;

      db.get(sql, [orderId], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row || null);
        }
      });
    });
  }

  /**
   * Update transaction status
   * @param {string} orderId - Order ID
   * @param {string} status - New status
   * @param {Object} additionalData - Additional data to update
   * @returns {Promise<boolean>} Success status
   */
  static async updateStatus(orderId, status, additionalData = {}) {
    return new Promise((resolve, reject) => {
      const updates = ['status = ?'];
      const values = [status];

      // Add additional fields to update
      Object.keys(additionalData).forEach(key => {
        updates.push(`${key} = ?`);
        values.push(additionalData[key]);
      });

      values.push(new Date().toISOString()); // updated_at
      values.push(orderId); // WHERE condition

      const sql = `
        UPDATE transactions 
        SET ${updates.join(', ')}, updated_at = ?
        WHERE order_id = ?
      `;

      db.run(sql, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  /**
   * Get user transactions
   * @param {string} userId - User ID
   * @param {number} limit - Limit results
   * @returns {Promise<Array>} User transactions
   */
  static async getUserTransactions(userId, limit = 10) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT t.*, sp.name as plan_name
        FROM transactions t
        LEFT JOIN subscription_plans sp ON t.plan_id = sp.id
        WHERE t.user_id = ?
        ORDER BY t.created_at DESC
        LIMIT ?
      `;

      db.all(sql, [userId, limit], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  /**
   * Get all transactions (admin)
   * @param {number} limit - Limit results
   * @param {number} offset - Offset for pagination
   * @returns {Promise<Array>} All transactions
   */
  static async getAllTransactions(limit = 50, offset = 0) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT t.*, sp.name as plan_name, u.username, u.email
        FROM transactions t
        LEFT JOIN subscription_plans sp ON t.plan_id = sp.id
        LEFT JOIN users u ON t.user_id = u.id
        ORDER BY t.created_at DESC
        LIMIT ? OFFSET ?
      `;

      db.all(sql, [limit, offset], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  /**
   * Generate unique order ID
   * @param {string} userId - User ID
   * @param {string} planId - Plan ID
   * @returns {string} Unique order ID
   */
  static generateOrderId(userId, planId) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `SOP-${userId.substring(0, 8)}-${planId.substring(0, 8)}-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * Clean up expired pending transactions (older than 10 minutes)
   * @returns {Promise<number>} Number of cleaned transactions
   */
  static async cleanupExpiredTransactions() {
    return new Promise((resolve, reject) => {
      // Calculate 10 minutes ago
      const tenMinutesAgo = new Date();
      tenMinutesAgo.setMinutes(tenMinutesAgo.getMinutes() - 10);
      const cutoffTime = tenMinutesAgo.toISOString();

      const sql = `
        UPDATE transactions
        SET status = 'expired'
        WHERE status = 'pending'
        AND created_at < ?
      `;

      db.run(sql, [cutoffTime], function(err) {
        if (err) {
          reject(err);
        } else {
          console.log(`🧹 Cleaned up ${this.changes} expired pending transactions`);
          resolve(this.changes);
        }
      });
    });
  }

  /**
   * Check if user has pending transaction for specific plan
   * @param {string} userId - User ID
   * @param {string} planId - Plan ID
   * @returns {Promise<boolean>} True if has pending transaction
   */
  static async hasPendingTransaction(userId, planId) {
    return new Promise((resolve, reject) => {
      // Calculate 10 minutes ago in JavaScript to ensure consistent timezone
      const tenMinutesAgo = new Date();
      tenMinutesAgo.setMinutes(tenMinutesAgo.getMinutes() - 10);
      const cutoffTime = tenMinutesAgo.toISOString();

      const sql = `
        SELECT COUNT(*) as count
        FROM transactions
        WHERE user_id = ?
        AND plan_id = ?
        AND status = 'pending'
        AND created_at > ?
      `;

      db.get(sql, [userId, planId, cutoffTime], (err, row) => {
        if (err) {
          reject(err);
        } else {
          console.log(`🔍 Checking pending transactions for user ${userId}, plan ${planId}`);
          console.log(`🕐 Cutoff time: ${cutoffTime}`);
          console.log(`📊 Found ${row.count} pending transactions`);
          resolve(row.count > 0);
        }
      });
    });
  }

  /**
   * Get user's pending transaction for specific plan
   * @param {string} userId - User ID
   * @param {string} planId - Plan ID
   * @returns {Promise<Object|null>} Pending transaction or null
   */
  static async getPendingTransaction(userId, planId) {
    return new Promise((resolve, reject) => {
      // Calculate 10 minutes ago in JavaScript to ensure consistent timezone
      const tenMinutesAgo = new Date();
      tenMinutesAgo.setMinutes(tenMinutesAgo.getMinutes() - 10);
      const cutoffTime = tenMinutesAgo.toISOString();

      const sql = `
        SELECT t.*, sp.name as plan_name
        FROM transactions t
        LEFT JOIN subscription_plans sp ON t.plan_id = sp.id
        WHERE t.user_id = ?
        AND t.plan_id = ?
        AND t.status = 'pending'
        AND t.created_at > ?
        ORDER BY t.created_at DESC
        LIMIT 1
      `;

      db.get(sql, [userId, planId, cutoffTime], (err, row) => {
        if (err) {
          reject(err);
        } else {
          if (row) {
            console.log(`✅ Found pending transaction: ${row.order_id}, created: ${row.created_at}`);
          } else {
            console.log(`❌ No pending transaction found for user ${userId}, plan ${planId}`);
          }
          resolve(row || null);
        }
      });
    });
  }

  /**
   * Get all user's pending transactions
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Array of pending transactions
   */
  static async getUserPendingTransactions(userId) {
    return new Promise((resolve, reject) => {
      // Calculate 10 minutes ago in JavaScript to ensure consistent timezone
      const tenMinutesAgo = new Date();
      tenMinutesAgo.setMinutes(tenMinutesAgo.getMinutes() - 10);
      const cutoffTime = tenMinutesAgo.toISOString();

      const sql = `
        SELECT t.*, sp.name as plan_name
        FROM transactions t
        LEFT JOIN subscription_plans sp ON t.plan_id = sp.id
        WHERE t.user_id = ?
        AND t.status = 'pending'
        AND t.created_at > ?
        ORDER BY t.created_at DESC
      `;

      db.all(sql, [userId, cutoffTime], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          console.log(`📋 Found ${rows.length} pending transactions for user ${userId}`);
          resolve(rows || []);
        }
      });
    });
  }
}

module.exports = Transaction;
